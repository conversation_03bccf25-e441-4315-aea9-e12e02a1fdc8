<template>
  <div class="comment-section">
    <div class="section-header">
      <h3>
        <el-icon><ChatDotRound /></el-icon>
        评论 ({{ comments.length }})
      </h3>
      <div v-if="ratingStats.totalRatings > 0" class="rating-summary">
        <el-rate 
          v-model="ratingStats.averageRating" 
          disabled 
          show-score 
          text-color="#ff9900"
          score-template="{value} 分"
        />
        <span class="rating-count">({{ ratingStats.totalRatings }}人评分)</span>
      </div>
    </div>

    <!-- 添加评论（点击触发显示表单） -->
    <div v-if="userStore.isLoggedIn">
      <div class="comment-trigger">
        <el-button type="primary" plain @click="showForm = !showForm">
          <el-icon><EditPen /></el-icon>
          {{ showForm ? '收起评论' : '写评论' }}
        </el-button>
      </div>
      <el-collapse-transition>
        <div v-show="showForm" class="comment-form">
          <el-form ref="commentFormRef" :model="commentForm" :rules="commentRules" label-width="60px">
            <el-form-item label="评分">
              <el-rate v-model="commentForm.rating" />
            </el-form-item>
            <el-form-item label="评论" prop="content">
              <el-input
                v-model="commentForm.content"
                type="textarea"
                :rows="4"
                placeholder="分享您的制作心得或对这道菜的看法..."
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSubmitComment" :loading="submitting">
                发表评论
              </el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-button text @click="showForm = false">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>
    </div>
    <div v-else class="login-prompt">
      <p>
        <router-link to="/login">登录</router-link> 后可以发表评论
      </p>
    </div>

    <!-- 评论列表 -->
    <div class="comments-list" v-loading="loading">
      <div v-if="comments.length === 0 && !loading" class="no-comments">
        <el-empty description="暂无评论，快来发表第一条评论吧！" />
      </div>
      
      <div v-for="comment in comments" :key="comment.id" class="comment-item">
        <div class="comment-header">
          <div class="user-info">
            <el-avatar :size="40" :src="comment.user?.avatar">
              {{ comment.user?.username?.charAt(0) }}
            </el-avatar>
            <div class="user-details">
              <span class="username">{{ comment.user?.username }}</span>
              <span v-if="comment.user?.role === 'INHERITOR'" class="user-badge">传承人</span>
              <span v-if="comment.user?.role === 'ADMIN'" class="user-badge admin">管理员</span>
              <div class="comment-meta">
                <el-rate 
                  v-if="comment.rating" 
                  v-model="comment.rating" 
                  disabled 
                  size="small"
                />
                <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
              </div>
            </div>
          </div>
          
          <div class="comment-actions" v-if="canManageComment(comment)">
            <el-dropdown @command="handleCommentAction">
              <el-button type="text" size="small">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`edit-${comment.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${comment.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
        
        <div class="comment-content">
          {{ comment.content }}
        </div>

        <!-- 回复按钮 -->
        <div class="comment-footer" v-if="userStore.isLoggedIn">
          <el-button
            type="text"
            size="small"
            @click="toggleReplyForm(comment.id)"
            :icon="ChatDotRound"
          >
            回复
          </el-button>
        </div>

        <!-- 回复表单 -->
        <el-collapse-transition>
          <div v-show="replyFormVisible[comment.id]" class="reply-form">
            <el-form :model="replyForms[comment.id] || {}" label-width="0">
              <el-form-item>
                <el-input
                  v-model="replyForms[comment.id].content"
                  type="textarea"
                  :rows="3"
                  :placeholder="`回复 ${comment.user?.username}...`"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  size="small"
                  @click="handleSubmitReply(comment.id)"
                  :loading="replySubmitting[comment.id]"
                >
                  发表回复
                </el-button>
                <el-button
                  size="small"
                  @click="cancelReply(comment.id)"
                >
                  取消
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-transition>

        <!-- 回复列表 -->
        <div v-if="comment.replies && comment.replies.length > 0" class="replies-list">
          <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
            <div class="reply-header">
              <div class="user-info">
                <el-avatar :size="32" :src="reply.user?.avatar">
                  {{ reply.user?.username?.charAt(0) }}
                </el-avatar>
                <div class="user-details">
                  <span class="username">{{ reply.user?.username }}</span>
                  <span v-if="reply.user?.role === 'INHERITOR'" class="user-badge">传承人</span>
                  <span v-if="reply.user?.role === 'ADMIN'" class="user-badge admin">管理员</span>
                  <span class="reply-time">{{ formatDate(reply.createdAt) }}</span>
                </div>
              </div>

              <div class="reply-actions" v-if="canManageComment(reply)">
                <el-dropdown @command="handleCommentAction">
                  <el-button type="text" size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${reply.id}`">编辑</el-dropdown-item>
                      <el-dropdown-item :command="`delete-${reply.id}`" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="reply-content">
              {{ reply.content }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="pagination.total > pagination.limit">
        <el-pagination
          v-model:current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.limit"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 编辑评论对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑评论" width="500px">
      <el-form ref="editFormRef" :model="editForm" :rules="commentRules" label-width="60px">
        <el-form-item label="评分">
          <el-rate v-model="editForm.rating" />
        </el-form-item>
        <el-form-item label="评论" prop="content">
          <el-input
            v-model="editForm.content"
            type="textarea"
            :rows="4"
            placeholder="分享您的制作心得或对这道菜的看法..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateComment" :loading="updating">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '../stores/user.js'
import { getRecipeComments, addComment, updateComment, deleteComment } from '../api/comments.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ChatDotRound, EditPen, MoreFilled } from '@element-plus/icons-vue'

const props = defineProps({
  recipeId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['commentAdded', 'commentUpdated', 'commentDeleted'])

const userStore = useUserStore()

const loading = ref(false)
const submitting = ref(false)
const updating = ref(false)
const showEditDialog = ref(false)
const showForm = ref(false)
const currentEditId = ref('')

const comments = ref([])
const ratingStats = reactive({
  averageRating: 0,
  totalRatings: 0
})

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

const commentFormRef = ref(null)
const editFormRef = ref(null)

const commentForm = reactive({
  content: '',
  rating: 0
})

const editForm = reactive({
  content: '',
  rating: 0
})

// 回复相关的响应式数据
const replyFormVisible = ref({})
const replyForms = ref({})
const replySubmitting = ref({})

const commentRules = {
  content: [
    { required: true, message: '请输入评论内容', trigger: 'blur' },
    { min: 2, message: '评论内容至少2个字符', trigger: 'blur' },
    { max: 500, message: '评论内容不能超过500个字符', trigger: 'blur' }
  ]
}

const formatDate = (dateString) => {
  try {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

const canManageComment = (comment) => {
  return userStore.isLoggedIn && 
    (comment.user?.id === userStore.user?.id || userStore.isAdmin)
}

const fetchComments = async () => {
  try {
    loading.value = true
    console.log('获取评论，菜谱ID:', props.recipeId)

    const response = await getRecipeComments(props.recipeId, {
      page: pagination.page,
      limit: pagination.limit
    })

    console.log('评论API响应:', response)

    // 处理响应数据
    if (response && typeof response === 'object') {
      comments.value = response.comments || []

      if (response.pagination) {
        pagination.total = response.pagination.total || 0
      }

      if (response.ratingStats) {
        ratingStats.averageRating = response.ratingStats.averageRating || 0
        ratingStats.totalRatings = response.ratingStats.totalRatings || 0
      }
    } else {
      comments.value = []
      pagination.total = 0
    }

    console.log('处理后的评论数据:', comments.value)
  } catch (error) {
    console.error('获取评论失败:', error)
    comments.value = []
    pagination.total = 0
    const errorMessage = error?.error || error?.message || '获取评论失败'
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleSubmitComment = async () => {
  console.log('开始提交评论...')

  if (!commentFormRef.value) {
    console.error('表单引用未找到')
    ElMessage.error('表单未初始化')
    return
  }

  if (!commentForm.content || commentForm.content.trim().length === 0) {
    ElMessage.error('请输入评论内容')
    return
  }

  try {
    submitting.value = true
    console.log('提交数据:', {
      recipeId: props.recipeId,
      content: commentForm.content.trim(),
      rating: commentForm.rating || null
    })

    const response = await addComment(props.recipeId, {
      content: commentForm.content.trim(),
      rating: commentForm.rating || null
    })

    console.log('评论提交成功:', response)
    ElMessage.success('评论发表成功')
    resetForm()
    fetchComments() // 重新加载评论列表

    // 处理响应数据
    if (response && response.comment) {
      emit('commentAdded', response.comment)
    } else {
      emit('commentAdded', response)
    }
  } catch (error) {
    console.error('发表评论错误:', error)
    const errorMessage = error?.error || error?.message || '发表评论失败'
    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  commentForm.content = ''
  commentForm.rating = 0
  commentFormRef.value?.resetFields()
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchComments()
}

const handleCommentAction = (command) => {
  const [action, commentId] = command.split('-')
  
  if (action === 'edit') {
    const comment = comments.value.find(c => c.id === commentId)
    if (comment) {
      editForm.content = comment.content
      editForm.rating = comment.rating || 0
      currentEditId.value = commentId
      showEditDialog.value = true
    }
  } else if (action === 'delete') {
    handleDeleteComment(commentId)
  }
}

const handleUpdateComment = async () => {
  try {
    await editFormRef.value.validate()
    updating.value = true
    
    await updateComment(currentEditId.value, {
      content: editForm.content,
      rating: editForm.rating || null
    })
    
    ElMessage.success('评论更新成功')
    showEditDialog.value = false
    fetchComments()
    emit('commentUpdated')
  } catch (error) {
    ElMessage.error(error.error || '更新评论失败')
  } finally {
    updating.value = false
  }
}

const handleDeleteComment = (commentId) => {
  ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await deleteComment(commentId)
      ElMessage.success('评论删除成功')
      fetchComments()
      emit('commentDeleted')
    } catch (error) {
      ElMessage.error(error.error || '删除评论失败')
    }
  }).catch(() => {})
}

// 回复相关方法
const toggleReplyForm = (commentId) => {
  replyFormVisible.value[commentId] = !replyFormVisible.value[commentId]

  if (replyFormVisible.value[commentId]) {
    // 初始化回复表单
    if (!replyForms.value[commentId]) {
      replyForms.value[commentId] = { content: '' }
    }
  }
}

const cancelReply = (commentId) => {
  replyFormVisible.value[commentId] = false
  if (replyForms.value[commentId]) {
    replyForms.value[commentId].content = ''
  }
}

const handleSubmitReply = async (parentCommentId) => {
  const replyForm = replyForms.value[parentCommentId]

  if (!replyForm || !replyForm.content || replyForm.content.trim().length === 0) {
    ElMessage.error('请输入回复内容')
    return
  }

  if (replyForm.content.trim().length < 2) {
    ElMessage.error('回复内容至少2个字符')
    return
  }

  if (replyForm.content.trim().length > 500) {
    ElMessage.error('回复内容不能超过500个字符')
    return
  }

  try {
    replySubmitting.value[parentCommentId] = true

    const response = await addComment(props.recipeId, {
      content: replyForm.content.trim(),
      parentId: parentCommentId
    })

    ElMessage.success('回复发表成功')

    // 清空回复表单并隐藏
    replyForm.content = ''
    replyFormVisible.value[parentCommentId] = false

    // 重新加载评论列表
    fetchComments()

    emit('commentAdded', response.comment)
  } catch (error) {
    console.error('发表回复错误:', error)
    const errorMessage = error?.error || error?.message || '发表回复失败'
    ElMessage.error(errorMessage)
  } finally {
    replySubmitting.value[parentCommentId] = false
  }
}

onMounted(() => {
  fetchComments()
})
</script>

<style scoped>
.comment-section {
  margin-top: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  margin: 0;
}

.rating-summary {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rating-count {
  color: #909399;
  font-size: 0.9rem;
}

.comment-trigger{margin-bottom:12px}
.comment-form {
  background: #f8fafc;
  padding: 20px;
  border-radius: var(--el-border-radius-base);
  border:1px solid #e2e8f0;
  margin-bottom: 20px;
}

.login-prompt {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.login-prompt a {
  color: #409eff;
  text-decoration: none;
}

.comments-list {
  min-height: 200px;
}

.no-comments {
  text-align: center;
  padding: 40px 0;
}

.comment-item {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.user-info {
  display: flex;
  gap: 12px;
}

.user-details {
  flex: 1;
}

.username {
  font-weight: bold;
  color: #303133;
  margin-right: 8px;
}

.user-badge {
  background: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  margin-right: 8px;
}

.user-badge.admin {
  background: #f56c6c;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 4px;
}

.comment-time {
  color: #909399;
  font-size: 0.85rem;
}

.comment-content {
  color: #606266;
  line-height: 1.6;
  margin-left: 52px;
}

.comment-footer {
  margin-top: 10px;
  margin-left: 52px;
}

.reply-form {
  margin: 15px 0 15px 52px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #409eff;
}

.replies-list {
  margin-left: 52px;
  margin-top: 15px;
  border-left: 2px solid #e4e7ed;
  padding-left: 20px;
}

.reply-item {
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.reply-header .user-info {
  gap: 10px;
}

.reply-header .user-details {
  font-size: 14px;
}

.reply-time {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

.reply-content {
  color: #303133;
  line-height: 1.6;
  font-size: 14px;
  margin-left: 42px;
}

.reply-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.reply-item:hover .reply-actions {
  opacity: 1;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .comment-content {
    margin-left: 0;
    margin-top: 10px;
  }
}
</style>
