import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// 获取用户的通知列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, unreadOnly = false } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const whereCondition = {
      userId: req.user.userId
    };

    if (unreadOnly === 'true') {
      whereCondition.isRead = false;
    }

    const notifications = await prisma.notification.findMany({
      where: whereCondition,
      include: {
        fromUser: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        },
        recipe: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        comment: {
          select: {
            id: true,
            content: true
          }
        },
        reply: {
          select: {
            id: true,
            content: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.notification.count({
      where: whereCondition
    });

    const unreadCount = await prisma.notification.count({
      where: {
        userId: req.user.userId,
        isRead: false
      }
    });

    res.json({
      notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      unreadCount
    });

  } catch (error) {
    console.error('获取通知列表错误:', error);
    res.status(500).json({ 
      error: '获取通知列表失败' 
    });
  }
});

// 获取未读通知数量
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const unreadCount = await prisma.notification.count({
      where: {
        userId: req.user.userId,
        isRead: false
      }
    });

    res.json({ unreadCount });

  } catch (error) {
    console.error('获取未读通知数量错误:', error);
    res.status(500).json({ 
      error: '获取未读通知数量失败' 
    });
  }
});

// 标记通知为已读
router.put('/:notificationId/read', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;

    // 检查通知是否存在且属于当前用户
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId }
    });

    if (!notification) {
      return res.status(404).json({ 
        error: '通知不存在' 
      });
    }

    if (notification.userId !== req.user.userId) {
      return res.status(403).json({ 
        error: '没有权限操作此通知' 
      });
    }

    // 更新为已读
    const updatedNotification = await prisma.notification.update({
      where: { id: notificationId },
      data: { isRead: true }
    });

    res.json({
      message: '通知已标记为已读',
      notification: updatedNotification
    });

  } catch (error) {
    console.error('标记通知已读错误:', error);
    res.status(500).json({ 
      error: '标记通知已读失败' 
    });
  }
});

// 标记所有通知为已读
router.put('/read-all', authenticateToken, async (req, res) => {
  try {
    const result = await prisma.notification.updateMany({
      where: {
        userId: req.user.userId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    res.json({
      message: '所有通知已标记为已读',
      updatedCount: result.count
    });

  } catch (error) {
    console.error('标记所有通知已读错误:', error);
    res.status(500).json({ 
      error: '标记所有通知已读失败' 
    });
  }
});

// 删除通知
router.delete('/:notificationId', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;

    // 检查通知是否存在且属于当前用户
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId }
    });

    if (!notification) {
      return res.status(404).json({ 
        error: '通知不存在' 
      });
    }

    if (notification.userId !== req.user.userId) {
      return res.status(403).json({ 
        error: '没有权限删除此通知' 
      });
    }

    await prisma.notification.delete({
      where: { id: notificationId }
    });

    res.json({
      message: '通知删除成功'
    });

  } catch (error) {
    console.error('删除通知错误:', error);
    res.status(500).json({ 
      error: '删除通知失败' 
    });
  }
});

export default router;
