<template>
  <div class="notification-center">
    <div class="notification-header">
      <h3>
        <el-icon><Bell /></el-icon>
        消息中心
      </h3>
      <div class="header-actions">
        <el-button 
          v-if="unreadCount > 0" 
          type="text" 
          size="small"
          @click="markAllAsRead"
          :loading="markingAllRead"
        >
          全部已读
        </el-button>
      </div>
    </div>

    <div class="notification-filters">
      <el-radio-group v-model="filterType" @change="fetchNotifications">
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="unread">未读</el-radio-button>
      </el-radio-group>
    </div>

    <div class="notifications-list" v-loading="loading">
      <div v-if="notifications.length === 0 && !loading" class="no-notifications">
        <el-empty description="暂无消息" />
      </div>

      <div 
        v-for="notification in notifications" 
        :key="notification.id" 
        class="notification-item"
        :class="{ 'unread': !notification.isRead }"
        @click="handleNotificationClick(notification)"
      >
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-text">{{ notification.content }}</div>
          <div class="notification-meta">
            <span class="notification-time">{{ formatDate(notification.createdAt) }}</span>
            <span v-if="notification.fromUser" class="from-user">
              来自: {{ notification.fromUser.username }}
            </span>
          </div>
        </div>
        
        <div class="notification-actions">
          <el-button 
            v-if="!notification.isRead"
            type="text" 
            size="small"
            @click.stop="markAsRead(notification.id)"
          >
            标记已读
          </el-button>
          <el-button 
            type="text" 
            size="small"
            @click.stop="deleteNotification(notification.id)"
          >
            删除
          </el-button>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="pagination.total > pagination.limit">
        <el-pagination
          v-model:current-page="pagination.page"
          :total="pagination.total"
          :page-size="pagination.limit"
          layout="prev, pager, next"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { 
  getNotifications, 
  markAsRead as markNotificationAsRead,
  markAllAsRead as markAllNotificationsAsRead,
  deleteNotification as deleteNotificationApi 
} from '@/api/notifications.js'

const router = useRouter()

const loading = ref(false)
const markingAllRead = ref(false)
const filterType = ref('all')

const notifications = ref([])
const unreadCount = ref(0)

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

const fetchNotifications = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      limit: pagination.limit
    }
    
    if (filterType.value === 'unread') {
      params.unreadOnly = true
    }

    const response = await getNotifications(params)
    
    if (response && typeof response === 'object') {
      notifications.value = response.notifications || []
      unreadCount.value = response.unreadCount || 0
      
      if (response.pagination) {
        pagination.total = response.pagination.total || 0
      }
    }
  } catch (error) {
    console.error('获取通知列表错误:', error)
    ElMessage.error('获取通知列表失败')
  } finally {
    loading.value = false
  }
}

const markAsRead = async (notificationId) => {
  try {
    await markNotificationAsRead(notificationId)
    
    // 更新本地状态
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.isRead = true
      unreadCount.value = Math.max(0, unreadCount.value - 1)
    }
    
    ElMessage.success('已标记为已读')
  } catch (error) {
    console.error('标记已读错误:', error)
    ElMessage.error('标记已读失败')
  }
}

const markAllAsRead = async () => {
  try {
    markingAllRead.value = true
    await markAllNotificationsAsRead()
    
    // 更新本地状态
    notifications.value.forEach(notification => {
      notification.isRead = true
    })
    unreadCount.value = 0
    
    ElMessage.success('所有消息已标记为已读')
  } catch (error) {
    console.error('标记所有已读错误:', error)
    ElMessage.error('标记所有已读失败')
  } finally {
    markingAllRead.value = false
  }
}

const deleteNotification = async (notificationId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条消息吗？', '提示', {
      type: 'warning'
    })
    
    await deleteNotificationApi(notificationId)
    
    // 从列表中移除
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      const notification = notifications.value[index]
      if (!notification.isRead) {
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      notifications.value.splice(index, 1)
      pagination.total = Math.max(0, pagination.total - 1)
    }
    
    ElMessage.success('消息删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除消息错误:', error)
      ElMessage.error('删除消息失败')
    }
  }
}

const handleNotificationClick = async (notification) => {
  // 如果未读，先标记为已读
  if (!notification.isRead) {
    await markAsRead(notification.id)
  }
  
  // 根据通知类型跳转到相应页面
  if (notification.recipeId) {
    router.push(`/recipes/${notification.recipeId}`)
  }
}

const handlePageChange = (page) => {
  pagination.page = page
  fetchNotifications()
}

const formatDate = (dateString) => {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now - date
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }
    
    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }
    
    // 小于1天
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }
    
    // 小于7天
    if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    }
    
    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateString
  }
}

onMounted(() => {
  fetchNotifications()
})

// 暴露未读数量给父组件
defineExpose({
  unreadCount
})
</script>

<style scoped>
.notification-center {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.notification-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  margin: 0;
}

.notification-filters {
  margin-bottom: 20px;
}

.notifications-list {
  min-height: 400px;
}

.no-notifications {
  text-align: center;
  padding: 60px 0;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.notification-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.notification-item.unread {
  background: #f0f9ff;
  border-color: #409eff;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #409eff;
  border-radius: 2px 0 0 2px;
}

.notification-item {
  position: relative;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.notification-text {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.notification-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.notification-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center {
    padding: 10px;
  }
  
  .notification-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .notification-actions {
    opacity: 1;
    align-self: flex-end;
  }
}
</style>
