import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// 导入路由
import authRoutes from './routes/auth.js';
import recipeRoutes from './routes/recipes.js';
import uploadRoutes from './routes/upload.js';
import applicationRoutes from './routes/applications.js';
import favoritesRoutes from './routes/favorites.js';
import commentsRoutes from './routes/comments.js';
import userRoutes from './routes/users.js';
import notificationsRoutes from './routes/notifications.js';

// 导入MinIO配置
import { initializeBucket, testMinioConnection } from './lib/minio.js';

// 配置环境变量
dotenv.config();

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(helmet({
  // 允许跨源资源加载（用于前端从 5173 加载 3000 的图片/音频）
  crossOriginResourcePolicy: { policy: 'cross-origin' }
}));
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173'], // Vue开发服务器地址
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 - 用于提供上传的文件（显式允许CORS）
app.use('/uploads', cors(), express.static(path.join(__dirname, 'uploads')));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/recipes', recipeRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/favorites', favoritesRoutes);
app.use('/api/comments', commentsRoutes);
app.use('/api/users', userRoutes);
app.use('/api/notifications', notificationsRoutes);

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: '数字乡味API服务运行正常',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: '接口不存在',
    path: req.originalUrl 
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ 
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? err.message : '请稍后重试'
  });
});

// 启动服务器
app.listen(PORT, async () => {
  console.log(`🚀 数字乡味API服务已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🌍 环境: ${process.env.NODE_ENV}`);
  console.log(`📊 健康检查: http://localhost:${PORT}/api/health`);

  // 初始化MinIO
  try {
    console.log('🔄 正在初始化MinIO...');
    await testMinioConnection();
    await initializeBucket();
    console.log('✅ MinIO初始化完成');
  } catch (error) {
    console.error('❌ MinIO初始化失败:', error.message);
    console.log('⚠️  文件上传功能可能无法正常工作');
  }
});

export default app;
