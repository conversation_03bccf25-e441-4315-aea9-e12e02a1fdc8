import express from 'express';
import prisma from '../lib/db.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取菜谱的评论列表
router.get('/recipe/:recipeId', async (req, res) => {
  try {
    const { recipeId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // 检查菜谱是否存在
    const recipe = await prisma.recipe.findUnique({
      where: { id: recipeId }
    });

    if (!recipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 获取评论列表（只获取顶级评论，不包括回复）
    const comments = await prisma.comment.findMany({
      where: {
        recipeId: recipeId,
        parentId: null // 只获取顶级评论
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                avatar: true,
                role: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    // 获取总数（只计算顶级评论）
    const total = await prisma.comment.count({
      where: {
        recipeId: recipeId,
        parentId: null
      }
    });

    // 获取评分统计（只统计顶级评论的评分）
    const ratingStats = await prisma.comment.aggregate({
      where: {
        recipeId: recipeId,
        parentId: null, // 只统计顶级评论
        rating: {
          not: null
        }
      },
      _avg: {
        rating: true
      },
      _count: {
        rating: true
      }
    });

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      ratingStats: {
        averageRating: ratingStats._avg.rating || 0,
        totalRatings: ratingStats._count.rating || 0
      }
    });

  } catch (error) {
    console.error('获取评论列表错误:', error);
    res.status(500).json({ 
      error: '获取评论列表失败' 
    });
  }
});

// 添加评论或回复
router.post('/recipe/:recipeId', authenticateToken, async (req, res) => {
  try {
    const { recipeId } = req.params;
    const { content, rating, parentId } = req.body;

    // 验证必填字段
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        error: '评论内容不能为空' 
      });
    }

    // 验证评分（回复不能有评分）
    if (parentId && rating !== undefined) {
      return res.status(400).json({
        error: '回复不能包含评分'
      });
    }

    if (rating !== undefined && (rating < 1 || rating > 5)) {
      return res.status(400).json({
        error: '评分必须在1-5之间'
      });
    }

    // 检查菜谱是否存在
    const recipe = await prisma.recipe.findUnique({
      where: { id: recipeId }
    });

    if (!recipe) {
      return res.status(404).json({ 
        error: '菜谱不存在' 
      });
    }

    // 如果是回复，验证父评论是否存在且属于该菜谱
    let parentComment = null;
    if (parentId) {
      parentComment = await prisma.comment.findUnique({
        where: { id: parentId },
        include: { user: true }
      });

      if (!parentComment) {
        return res.status(404).json({
          error: '要回复的评论不存在'
        });
      }

      if (parentComment.recipeId !== recipeId) {
        return res.status(400).json({
          error: '评论不属于该菜谱'
        });
      }

      // 确保只能回复顶级评论（一级回复限制）
      if (parentComment.parentId !== null) {
        return res.status(400).json({
          error: '不能回复回复，只能回复原始评论'
        });
      }
    }

    // 创建评论或回复
    const comment = await prisma.comment.create({
      data: {
        content: content.trim(),
        rating: rating ? parseInt(rating) : null,
        userId: req.user.userId,
        recipeId: recipeId,
        parentId: parentId || null
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        }
      }
    });

    // 创建通知 - 临时禁用，等待数据库枚举类型修复
    // TODO: 修复 NotificationType 枚举后重新启用
    /*
    if (parentId && parentComment) {
      // 如果是回复，给原评论作者发送通知（不给自己发通知）
      if (parentComment.userId !== req.user.userId) {
        await prisma.notification.create({
          data: {
            type: 'COMMENT_REPLY',
            title: '您的评论收到了回复',
            content: `${req.user.username} 回复了您的评论：${content.trim().substring(0, 50)}${content.trim().length > 50 ? '...' : ''}`,
            userId: parentComment.userId,
            fromUserId: req.user.userId,
            recipeId: recipeId,
            commentId: parentId,
            replyId: comment.id
          }
        });
      }
    } else {
      // 如果是新评论，给菜谱作者发送通知（不给自己发通知）
      const recipe = await prisma.recipe.findUnique({
        where: { id: recipeId },
        select: { authorId: true, name: true }
      });

      if (recipe && recipe.authorId !== req.user.userId) {
        await prisma.notification.create({
          data: {
            type: 'RECIPE_COMMENT',
            title: '您的菜谱收到了新评论',
            content: `${req.user.username} 评论了您的菜谱《${recipe.name}》：${content.trim().substring(0, 50)}${content.trim().length > 50 ? '...' : ''}`,
            userId: recipe.authorId,
            fromUserId: req.user.userId,
            recipeId: recipeId,
            commentId: comment.id
          }
        });
      }
    }
    */

    res.status(201).json({
      message: parentId ? '回复添加成功' : '评论添加成功',
      comment
    });

  } catch (error) {
    console.error('添加评论错误:', error);
    res.status(500).json({ 
      error: '添加评论失败' 
    });
  }
});

// 更新评论
router.put('/:commentId', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { content, rating } = req.body;

    // 验证必填字段
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ 
        error: '评论内容不能为空' 
      });
    }

    // 验证评分
    if (rating !== undefined && (rating < 1 || rating > 5)) {
      return res.status(400).json({ 
        error: '评分必须在1-5之间' 
      });
    }

    // 检查评论是否存在
    const existingComment = await prisma.comment.findUnique({
      where: { id: commentId }
    });

    if (!existingComment) {
      return res.status(404).json({ 
        error: '评论不存在' 
      });
    }

    // 检查权限（只有评论作者或管理员可以修改）
    if (existingComment.userId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '没有权限修改此评论' 
      });
    }

    // 更新评论
    const comment = await prisma.comment.update({
      where: { id: commentId },
      data: {
        content: content.trim(),
        rating: rating ? parseInt(rating) : null
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar: true,
            role: true
          }
        }
      }
    });

    res.json({
      message: '评论更新成功',
      comment
    });

  } catch (error) {
    console.error('更新评论错误:', error);
    res.status(500).json({ 
      error: '更新评论失败' 
    });
  }
});

// 删除评论
router.delete('/:commentId', authenticateToken, async (req, res) => {
  try {
    const { commentId } = req.params;

    // 检查评论是否存在
    const existingComment = await prisma.comment.findUnique({
      where: { id: commentId }
    });

    if (!existingComment) {
      return res.status(404).json({ 
        error: '评论不存在' 
      });
    }

    // 检查权限（只有评论作者或管理员可以删除）
    if (existingComment.userId !== req.user.userId && req.user.role !== 'ADMIN') {
      return res.status(403).json({ 
        error: '没有权限删除此评论' 
      });
    }

    // 删除评论
    await prisma.comment.delete({
      where: { id: commentId }
    });

    res.json({
      message: '评论删除成功'
    });

  } catch (error) {
    console.error('删除评论错误:', error);
    res.status(500).json({ 
      error: '删除评论失败' 
    });
  }
});

// 获取用户的评论列表
router.get('/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const comments = await prisma.comment.findMany({
      where: {
        userId: userId
      },
      include: {
        recipe: {
          select: {
            id: true,
            name: true,
            dialectName: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    const total = await prisma.comment.count({
      where: {
        userId: userId
      }
    });

    res.json({
      comments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('获取用户评论列表错误:', error);
    res.status(500).json({ 
      error: '获取用户评论列表失败' 
    });
  }
});

export default router;
