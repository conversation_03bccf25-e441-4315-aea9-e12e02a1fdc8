<template>
  <div class="notification-view">
    <div class="container">
      <NotificationCenter />
    </div>
  </div>
</template>

<script setup>
import NotificationCenter from '@/components/NotificationCenter.vue'
</script>

<style scoped>
.notification-view {
  min-height: calc(100vh - 120px);
  background: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
</style>
