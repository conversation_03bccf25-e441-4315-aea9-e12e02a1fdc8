import api from './index.js'

// 获取通知列表
export const getNotifications = async (params = {}) => {
  try {
    const data = await api.get('/notifications', { params })
    console.log('获取通知列表响应:', data)
    return data
  } catch (error) {
    console.error('获取通知列表API错误:', error)
    throw error
  }
}

// 获取未读通知数量
export const getUnreadCount = async () => {
  try {
    const data = await api.get('/notifications/unread-count')
    console.log('获取未读通知数量响应:', data)
    return data
  } catch (error) {
    console.error('获取未读通知数量API错误:', error)
    throw error
  }
}

// 标记通知为已读
export const markAsRead = async (notificationId) => {
  try {
    const data = await api.put(`/notifications/${notificationId}/read`)
    console.log('标记通知已读响应:', data)
    return data
  } catch (error) {
    console.error('标记通知已读API错误:', error)
    throw error
  }
}

// 标记所有通知为已读
export const markAllAsRead = async () => {
  try {
    const data = await api.put('/notifications/read-all')
    console.log('标记所有通知已读响应:', data)
    return data
  } catch (error) {
    console.error('标记所有通知已读API错误:', error)
    throw error
  }
}

// 删除通知
export const deleteNotification = async (notificationId) => {
  try {
    const data = await api.delete(`/notifications/${notificationId}`)
    console.log('删除通知响应:', data)
    return data
  } catch (error) {
    console.error('删除通知API错误:', error)
    throw error
  }
}
