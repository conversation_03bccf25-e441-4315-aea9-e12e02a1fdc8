<template>
  <el-popover
    placement="bottom-end"
    :width="400"
    trigger="click"
    popper-class="notification-popover"
  >
    <template #reference>
      <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
        <el-button type="text" class="notification-bell">
          <el-icon :size="20"><Bell /></el-icon>
        </el-button>
      </el-badge>
    </template>

    <div class="notification-dropdown">
      <div class="dropdown-header">
        <span class="title">消息通知</span>
        <div class="header-actions">
          <el-button 
            v-if="unreadCount > 0" 
            type="text" 
            size="small"
            @click="markAllAsRead"
            :loading="markingAllRead"
          >
            全部已读
          </el-button>
          <el-button 
            type="text" 
            size="small"
            @click="goToNotificationCenter"
          >
            查看全部
          </el-button>
        </div>
      </div>

      <div class="notifications-preview" v-loading="loading">
        <div v-if="notifications.length === 0 && !loading" class="no-notifications">
          <el-empty description="暂无新消息" :image-size="60" />
        </div>

        <div 
          v-for="notification in notifications.slice(0, 5)" 
          :key="notification.id"
          class="notification-preview-item"
          :class="{ 'unread': !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-text">{{ notification.content }}</div>
            <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
          </div>
          <div v-if="!notification.isRead" class="unread-dot"></div>
        </div>

        <div v-if="notifications.length > 5" class="more-notifications">
          还有 {{ notifications.length - 5 }} 条消息...
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { 
  getNotifications, 
  getUnreadCount,
  markAsRead as markNotificationAsRead,
  markAllAsRead as markAllNotificationsAsRead
} from '@/api/notifications.js'

const router = useRouter()

const loading = ref(false)
const markingAllRead = ref(false)
const notifications = ref([])
const unreadCount = ref(0)

let pollInterval = null

const fetchNotifications = async () => {
  try {
    loading.value = true
    
    const response = await getNotifications({
      page: 1,
      limit: 10,
      unreadOnly: false
    })
    
    if (response && typeof response === 'object') {
      notifications.value = response.notifications || []
      unreadCount.value = response.unreadCount || 0
    }
  } catch (error) {
    console.error('获取通知列表错误:', error)
  } finally {
    loading.value = false
  }
}

const fetchUnreadCount = async () => {
  try {
    const response = await getUnreadCount()
    if (response && typeof response === 'object') {
      unreadCount.value = response.unreadCount || 0
    }
  } catch (error) {
    console.error('获取未读数量错误:', error)
  }
}

const markAllAsRead = async () => {
  try {
    markingAllRead.value = true
    await markAllNotificationsAsRead()
    
    // 更新本地状态
    notifications.value.forEach(notification => {
      notification.isRead = true
    })
    unreadCount.value = 0
    
    ElMessage.success('所有消息已标记为已读')
  } catch (error) {
    console.error('标记所有已读错误:', error)
    ElMessage.error('标记所有已读失败')
  } finally {
    markingAllRead.value = false
  }
}

const handleNotificationClick = async (notification) => {
  // 如果未读，先标记为已读
  if (!notification.isRead) {
    try {
      await markNotificationAsRead(notification.id)
      notification.isRead = true
      unreadCount.value = Math.max(0, unreadCount.value - 1)
    } catch (error) {
      console.error('标记已读错误:', error)
    }
  }
  
  // 根据通知类型跳转到相应页面
  if (notification.recipeId) {
    router.push(`/recipe/${notification.recipeId}`)
  }
}

const goToNotificationCenter = () => {
  router.push('/notifications')
}

const formatTime = (dateString) => {
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now - date
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }
    
    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }
    
    // 小于1天
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }
    
    // 小于7天
    if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    }
    
    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return dateString
  }
}

// 定期轮询未读数量
const startPolling = () => {
  // 每30秒检查一次未读数量
  pollInterval = setInterval(() => {
    fetchUnreadCount()
  }, 30000)
}

const stopPolling = () => {
  if (pollInterval) {
    clearInterval(pollInterval)
    pollInterval = null
  }
}

onMounted(() => {
  fetchNotifications()
  startPolling()
})

onUnmounted(() => {
  stopPolling()
})

// 暴露未读数量给父组件
defineExpose({
  unreadCount,
  refresh: fetchNotifications
})
</script>

<style scoped>
.notification-bell {
  color: #606266;
  padding: 8px;
}

.notification-bell:hover {
  color: #409eff;
}

.notification-dropdown {
  max-height: 500px;
  overflow: hidden;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.dropdown-header .title {
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.notifications-preview {
  max-height: 400px;
  overflow-y: auto;
}

.no-notifications {
  padding: 40px 20px;
  text-align: center;
}

.notification-preview-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.notification-preview-item:hover {
  background: #f8f9fa;
}

.notification-preview-item.unread {
  background: #f0f9ff;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 4px;
}

.more-notifications {
  padding: 12px 16px;
  text-align: center;
  color: #909399;
  font-size: 12px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}
</style>

<style>
.notification-popover {
  padding: 0 !important;
}
</style>
