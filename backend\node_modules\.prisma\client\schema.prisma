// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String?  @unique
  password  String
  role      UserRole @default(USER)
  avatar    String?
  bio       String? // 个人简介
  isActive  Boolean  @default(true) // 账号是否激活，false表示被禁用
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  recipes               Recipe[]
  comments              Comment[]
  favorites             UserFavorite[]
  inheritorApplication  InheritorApplication?
  reviewedApplications  InheritorApplication[] @relation("ReviewedApplications")
  sentNotifications     Notification[]         @relation("SentNotifications")
  receivedNotifications Notification[]         @relation("ReceivedNotifications")

  @@map("users")
}

// 传承人申请表
model InheritorApplication {
  id             String            @id @default(cuid())
  userId         String            @unique
  realName       String // 真实姓名
  phone          String // 联系电话
  region         String // 所在地区
  specialties    String // 擅长菜系/特色菜
  experience     String // 从业经验描述
  certifications String? // 相关证书/资质
  introduction   String // 个人介绍
  status         ApplicationStatus @default(PENDING)
  appliedAt      DateTime          @default(now())
  reviewedAt     DateTime? // 审核时间
  reviewedBy     String? // 审核人ID
  reviewComment  String? // 审核意见

  user     User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  reviewer User? @relation("ReviewedApplications", fields: [reviewedBy], references: [id])

  @@map("inheritor_applications")
}

// 用户角色枚举
enum UserRole {
  USER // 普通用户
  ADMIN // 管理员
  INHERITOR // 传承人
}

// 传承人申请状态枚举
enum ApplicationStatus {
  PENDING // 待审核
  APPROVED // 已通过
  REJECTED // 已拒绝
}

// 菜谱表
model Recipe {
  id           String  @id @default(cuid())
  name         String // 菜名
  dialectName  String // 方言名称
  dialectAudio String? // 方言音频文件路径
  region       String // 地区
  ingredients  String // 食材列表
  steps        String // 制作步骤
  image        String? // 菜品图片
  description  String? // 描述
  difficulty   Int     @default(1) // 难度等级 1-5
  cookTime     Int? // 制作时间(分钟)
  servings     Int? // 份量
  isPublished  Boolean @default(true)
  viewCount    Int     @default(0)

  // 文化传承相关字段
  culturalHistory  String? // 历史渊源
  regionalFeature  String? // 地域特色
  culturalMeaning  String? // 文化寓意
  inheritanceStory String? // 传承故事

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  authorId      String
  author        User             @relation(fields: [authorId], references: [id])
  categories    RecipeCategory[]
  comments      Comment[]
  favorites     UserFavorite[]
  notifications Notification[]

  @@map("recipes")
}

// 分类表
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  icon        String?
  createdAt   DateTime @default(now())

  // 关联关系
  recipes RecipeCategory[]

  @@map("categories")
}

// 菜谱分类关联表
model RecipeCategory {
  recipeId   String
  categoryId String
  recipe     Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([recipeId, categoryId])
  @@map("recipe_categories")
}

// 评论表
model Comment {
  id        String   @id @default(cuid())
  content   String
  rating    Int? // 评分 1-5
  parentId  String?  @map("parent_id") // 父评论ID，用于回复功能
  createdAt DateTime @default(now())

  // 关联关系
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  recipeId String
  recipe   Recipe @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  // 回复关联
  parent  Comment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies Comment[] @relation("CommentReplies")

  // 通知关联
  commentNotifications Notification[] @relation("CommentNotifications")
  replyNotifications   Notification[] @relation("ReplyNotifications")

  @@map("comments")
}

// 用户收藏表
model UserFavorite {
  userId    String
  recipeId  String
  createdAt DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id])
  recipe Recipe @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  @@id([userId, recipeId])
  @@map("user_favorites")
}

// 通知类型枚举
enum NotificationType {
  COMMENT_REPLY // 评论回复
  RECIPE_COMMENT // 菜谱评论
  SYSTEM // 系统通知

  @@map("notification_type")
}

// 通知表
model Notification {
  id        String           @id @default(cuid())
  type      NotificationType
  title     String
  content   String
  isRead    Boolean          @default(false) @map("is_read")
  createdAt DateTime         @default(now()) @map("created_at")

  // 接收通知的用户
  userId String @map("user_id")
  user   User   @relation("ReceivedNotifications", fields: [userId], references: [id], onDelete: Cascade)

  // 触发通知的用户（可选）
  fromUserId String? @map("from_user_id")
  fromUser   User?   @relation("SentNotifications", fields: [fromUserId], references: [id], onDelete: Cascade)

  // 相关的菜谱（可选）
  recipeId String? @map("recipe_id")
  recipe   Recipe? @relation(fields: [recipeId], references: [id], onDelete: Cascade)

  // 相关的评论（可选）
  commentId String?  @map("comment_id")
  comment   Comment? @relation("CommentNotifications", fields: [commentId], references: [id], onDelete: Cascade)

  // 相关的回复评论（可选）
  replyId String?  @map("reply_id")
  reply   Comment? @relation("ReplyNotifications", fields: [replyId], references: [id], onDelete: Cascade)

  @@map("notifications")
}
