import api from './index.js'

// 获取菜谱的评论列表
export const getRecipeComments = async (recipeId, params = {}) => {
  try {
    // api已经通过拦截器返回了response.data，所以这里直接返回
    const data = await api.get(`/comments/recipe/${recipeId}`, { params })
    console.log('获取评论原始响应:', data)
    return data
  } catch (error) {
    console.error('获取评论API错误:', error)
    throw error
  }
}

// 添加评论或回复
export const addComment = async (recipeId, commentData) => {
  try {
    // api已经通过拦截器返回了response.data，所以这里直接返回
    const data = await api.post(`/comments/recipe/${recipeId}`, commentData)
    console.log('添加评论原始响应:', data)
    return data
  } catch (error) {
    console.error('添加评论API错误:', error)
    throw error
  }
}

// 更新评论
export const updateComment = async (commentId, commentData) => {
  try {
    const data = await api.put(`/comments/${commentId}`, commentData)
    return data
  } catch (error) {
    throw error
  }
}

// 删除评论
export const deleteComment = async (commentId) => {
  try {
    const data = await api.delete(`/comments/${commentId}`)
    return data
  } catch (error) {
    throw error
  }
}

// 获取用户的评论列表
export const getUserComments = async (userId, params = {}) => {
  try {
    const data = await api.get(`/comments/user/${userId}`, { params })
    return data
  } catch (error) {
    throw error
  }
}
